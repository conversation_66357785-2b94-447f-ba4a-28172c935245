use ndarray::Array2;
use raylib::prelude::*;

const SIMULATION_STEPS_PER_SECOND: u32 = 50;

fn main() {
    let (mut rl, thread) = init()
        .size(800, 800)
        .title("Infinite Conway's Game of Life")
        .build();

    let cell_size = 10;

    let board_width = rl.get_screen_width() / cell_size;
    let board_height = rl.get_screen_height() / cell_size;

    let mut board: Array2<f32> =
        Array2::from_shape_fn((board_width as usize, board_height as usize), |(_, _)| 0.0);

    let kernel: Array2<f32> =
        Array2::from_shape_fn((3, 3), |(i, j)| (i != 1 && j != 1) as i32 as f32);

    let mut is_running = false;
    let mut last_time = rl.get_time();
    let mut last_frame_time = 0.0;

    while !rl.window_should_close() {
        let current_time = rl.get_time();
        let fps = rl.get_fps();

        let mut d = rl.begin_drawing(&thread);
        d.clear_background(Color::WHITE);

        // UI

        d.draw_text(&format!("FPS: {}", fps), 10, 10, 20, Color::GRAY);

        // Mouse input handling

        if d.is_mouse_button_down(MouseButton::MOUSE_BUTTON_LEFT) {
            let mouse_x = d.get_mouse_x() / cell_size;
            let mouse_y = d.get_mouse_y() / cell_size;
            board[(mouse_y as usize, mouse_x as usize)] = 1.0;
        }

        // Keyboard input handling

        if d.is_key_pressed(KeyboardKey::KEY_SPACE) {
            is_running = !is_running;
        }

        // Simulation Logic

        if is_running {
            let elapsed_time = current_time - last_time;
            last_time = current_time;
            last_frame_time += elapsed_time;
            if last_frame_time >= (1.0 / SIMULATION_STEPS_PER_SECOND as f32) as f64 {
                last_frame_time = 0.0;
                let dt = 1.0 / SIMULATION_STEPS_PER_SECOND as f32;

                // Run
                let new_board = convolve(&board, &kernel);
                // board = &board + dt * conway_growth(&board);
                // board = clip(&board);

                // Rendering
                render_board(&mut d, &new_board, cell_size);
            }
        } else {
            render_board(&mut d, &board, cell_size);
        }
    }
}

fn render_board(d: &mut RaylibDrawHandle, board: &Array2<f32>, cell_size: i32) {
    for ((row, col), &value) in board.indexed_iter() {
        if value > 0.0 {
            d.draw_rectangle(
                col as i32 * cell_size,
                row as i32 * cell_size,
                cell_size,
                cell_size,
                Color::BLACK,
            );
        }
    }
}

fn conway_growth(image: &Array2<f32>) -> Array2<f32> {
    image.map(|&x| match x {
        3.0 => 1.0,
        2.0 => 0.0,
        _ => -1.0,
    })
}

fn clip(image: &Array2<f32>) -> Array2<f32> {
    image.map(|&x| {
        if x < 0.0 {
            0.0
        } else if x > 1.0 {
            1.0
        } else {
            x
        }
    })
}

fn convolve(image: &Array2<f32>, kernel: &Array2<f32>) -> Array2<f32> {
}
